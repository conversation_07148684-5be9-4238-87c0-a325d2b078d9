import adapter from '@sveltejs/adapter-node';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
  preprocess: vitePreprocess(),
  kit: {
    adapter: adapter({
      // Adapter options for Render deployment
      // Explicitly set to use environment variables
      env: {
        host: 'HOST',
        port: 'PORT',
      },
      // Ensure we're listening on all interfaces
      precompress: true,
    }),
    // Use kit.alias instead of tsconfig paths
    alias: {
      $lib: './src/lib',
      $components: './src/components',
      // Add other aliases as needed
    },
    // Configure paths for static files
    files: {
      // Ensure static files are properly served
      assets: 'static',
    },
    // Enable service worker
    serviceWorker: {
      register: true,
    },
  },
};

export default config;
