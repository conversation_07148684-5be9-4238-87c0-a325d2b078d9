import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const user = await getUserFromToken(cookies);
    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Remove subscription from database
    await prisma.pushSubscription.deleteMany({
      where: { userId: user.id },
    });

    // Update notification settings
    await prisma.notificationSettings.upsert({
      where: { userId: user.id },
      update: { pushEnabled: false },
      create: {
        userId: user.id,
        pushEnabled: false,
        emailEnabled: true,
        browserEnabled: true,
        jobMatchEnabled: true,
        applicationStatusEnabled: true,
        automationEnabled: true,
      },
    });

    console.log(`Push subscription removed for user ${user.id}`);
    return json({ success: true });
  } catch (error) {
    console.error('Error removing push subscription:', error);
    return json({ error: 'Failed to unsubscribe' }, { status: 500 });
  }
};
