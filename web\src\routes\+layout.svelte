<script>
  import '../app.css';
  import Header from '$components/layout/Header.svelte';
  import { getStores } from '$app/stores';
  import Footer from '../components/layout/Footer.svelte';
  import { Toaster } from '$lib/components/ui/sonner';
  import { onMount, onDestroy } from 'svelte';
  import { CookieConsent } from '$components/cookie-consent';
  import { browser } from '$app/environment';
  import { initAnalytics, trackPageView } from '$lib/utils/analytics';
  import { cleanupBotpress } from '$lib/botpress';
  import GlobalPricingModal from '$lib/components/ui/pricing/GlobalPricingModal.svelte';
  import { KeyboardShortcutsInitializer } from '$components/keyboard-shortcuts';
  import { initUserProfile } from '$lib/stores/user-profile';
  import { initThemeFromServer, initAccountFromServer } from '$lib/stores/store';
  import { checkServiceWorkerRegistration, subscribeUser } from '$lib/server/push-notifications';

  // Initialize with default values
  let hideNavbar = $state(false);
  let isDashboard = $state(false);
  let currentPath = $state('');
  let hideCookieConsent = $state(false);
  let pageStore;

  // Only access the page store in the browser to avoid SSR issues
  if (browser) {
    const stores = getStores();
    pageStore = stores.page;

    // Update values based on current path
    $effect(() => {
      if (pageStore && $pageStore) {
        hideNavbar = $pageStore.url.pathname.startsWith('/auth');
        isDashboard = $pageStore.url.pathname.startsWith('/dashboard');
        currentPath = $pageStore.url.pathname;

        // Don't show cookie consent on auth, dashboard, or cookie policy pages
        hideCookieConsent =
          hideNavbar || isDashboard || $pageStore.url.pathname.startsWith('/legal/cookie-policy');
      }
    });
  }

  const { data, children } = $props();
  const user = data?.user;

  // Initialize the user profile store with the user data
  $effect(() => {
    if (user) {
      console.log('Initializing user profile store with:', user);
      initUserProfile(user);

      try {
        // @ts-ignore - Access user preferences which might not be in the type definition
        const preferences = user?.preferences?.account;

        if (preferences) {
          // Initialize theme from user preferences if available
          if (preferences.accessibility?.theme) {
            console.log(
              'Initializing theme from user preferences:',
              preferences.accessibility.theme
            );
            initThemeFromServer(preferences.accessibility.theme);
          }

          // Initialize account preferences
          console.log('Initializing account preferences from user data');
          initAccountFromServer(preferences);
        }
      } catch (error) {
        console.log('Error initializing preferences from user data:', error);
      }
    }
  });

  onMount(() => {
    // Initialize analytics if allowed by cookie preferences
    initAnalytics();

    // Handle page navigation to clean up Botpress when not on the contact page
    if (currentPath && currentPath !== '/contact') {
      console.log('Not on contact page, ensuring Botpress is cleaned up');
      cleanupBotpress();
    }

    // Track the current path for analytics and cleanup
    if (currentPath) {
      // Path has been updated in the reactive block above
      console.log('Path changed to:', currentPath);
    }

    // Track initial page view
    if (pageStore && $pageStore) {
      trackPageView($pageStore.url.pathname);
    } else if (currentPath) {
      trackPageView(currentPath);
    }

    // Check if service worker is registered
    checkServiceWorkerRegistration().then((isRegistered) => {
      if (!isRegistered) {
        console.log('Service worker not registered, subscribing user...');
        subscribeUser();
      }
    });
  });

  // Final cleanup when the app is unmounted
  onDestroy(() => {
    if (browser) {
      cleanupBotpress();
    }
  });
</script>

{#if hideNavbar}
  <!-- Auth pages have their own layout -->
  {@render children()}
{:else if isDashboard}
  <!-- Dashboard pages use the dashboard layout -->
  {@render children()}
{:else}
  <!-- Regular pages use the main layout -->
  <Header currentUser={user} />
  <main>
    {@render children()}
  </main>
  <Footer {data} />
{/if}

<Toaster />

<!-- Only show cookie consent banner in browser and not on auth/dashboard pages -->
{#if browser && !hideCookieConsent}
  <CookieConsent />
{/if}

<!-- Global pricing modal that can be triggered from anywhere in the app -->
<GlobalPricingModal />

<!-- Initialize keyboard shortcuts -->
<KeyboardShortcutsInitializer />
