import webpush from 'web-push';
import { dev } from '$app/environment';

// VAPID keys - use environment variables or fallback to generated keys
const VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY;
const VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY;
const VAPID_EMAIL = process.env.VAPID_EMAIL;

// Configure web-push
webpush.setVapidDetails(VAPID_EMAIL, VAPID_PUBLIC_KEY, VAPID_PRIVATE_KEY);

if (dev) {
  console.log('✅ VAPID keys configured for push notifications');
}

export interface PushNotificationData {
  title: string;
  message: string;
  url?: string;
  icon?: string;
  badge?: string;
  data?: Record<string, any>;
}

/**
 * Send push notification to a subscription
 * Note: <PERSON>rows<PERSON> handles subscription persistence, no database storage needed
 */
export async function sendPushNotification(
  subscription: any, // Browser PushSubscription object
  notificationData: PushNotificationData
): Promise<boolean> {
  try {
    const payload = JSON.stringify({
      title: notificationData.title,
      message: notificationData.message,
      body: notificationData.message,
      url: notificationData.url || '/dashboard/notifications',
      icon: notificationData.icon || '/favicon.ico',
      badge: notificationData.badge || '/favicon.ico',
      data: notificationData.data || {},
    });

    if (dev) {
      console.log('[DEV] Would send push notification:', payload);
      return true;
    }

    const result = await webpush.sendNotification(subscription, payload);
    console.log('Push notification sent:', result.statusCode);
    return true;
  } catch (error: any) {
    console.error('Failed to send push notification:', error);
    return false;
  }
}

/**
 * Get VAPID public key for client-side subscription
 */
export function getVapidPublicKey(): string {
  return VAPID_PUBLIC_KEY;
}
