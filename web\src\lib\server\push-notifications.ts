import webpush, { type PushSubscription } from 'web-push';

// VAPID keys from environment
const VAPID_PUBLIC_KEY = process.env.VAPID_PUBLIC_KEY;
const VAPID_PRIVATE_KEY = process.env.VAPID_PRIVATE_KEY;
const VAPID_EMAIL = process.env.VAPID_EMAIL || 'mailto:<EMAIL>';

// Configure web-push
webpush.setVapidDetails(VAPID_EMAIL, VAPID_PUBLIC_KEY, VAPID_PRIVATE_KEY);

export interface PushNotificationData {
  title: string;
  message: string;
  url?: string;
  icon?: string;
  badge?: string;
  data?: Record<string, any>;
}

/**
 * Send push notification to a subscription
 * Note: <PERSON>rowser handles subscription persistence, no database storage needed
 */
export async function sendPushNotification(
  subscription: PushSubscription,
  notificationData: PushNotificationData
): Promise<boolean> {
  try {
    if (!VAPID_PUBLIC_KEY || !VAPID_PRIVATE_KEY) {
      console.warn('VAPID keys not configured, cannot send push notification');
      return false;
    }

    const payload = JSON.stringify({
      title: notificationData.title,
      message: notificationData.message,
      body: notificationData.message,
      url: notificationData.url || '/dashboard/notifications',
      icon: notificationData.icon || '/favicon.ico',
      badge: notificationData.badge || '/favicon.ico',
      data: notificationData.data || {},
    });

    const result = await webpush.sendNotification(subscription, payload);
    console.log('Push notification sent:', result.statusCode);
    return true;
  } catch (error: any) {
    console.error('Failed to send push notification:', error);
    return false;
  }
}

/**
 * Get VAPID public key for client-side subscription
 */
export function getVapidPublicKey(): string | null {
  return VAPID_PUBLIC_KEY || null;
}

/**
 * Send push notification to a user by their ID
 */
export async function sendPushNotificationToUser(
  userId: string,
  notificationData: PushNotificationData
): Promise<boolean> {
  try {
    const { prisma } = await import('./prisma');

    // Get user's push subscriptions
    const subscriptions = await prisma.pushSubscription.findMany({
      where: { userId },
    });

    if (subscriptions.length === 0) {
      console.log(`No push subscriptions found for user ${userId}`);
      return false;
    }

    // Send to all user's subscriptions
    const results = await Promise.allSettled(
      subscriptions.map((sub) => {
        const pushSubscription: PushSubscription = {
          endpoint: sub.endpoint,
          keys: {
            p256dh: sub.p256dh || '',
            auth: sub.auth || '',
          },
        };
        return sendPushNotification(pushSubscription, notificationData);
      })
    );

    const successCount = results.filter((r) => r.status === 'fulfilled' && r.value).length;
    console.log(
      `Sent push notification to ${successCount}/${subscriptions.length} subscriptions for user ${userId}`
    );

    return successCount > 0;
  } catch (error) {
    console.error('Error sending push notification to user:', error);
    return false;
  }
}
