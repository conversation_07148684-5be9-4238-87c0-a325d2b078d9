/// <reference types="@sveltejs/kit" />
/// <reference no-default-lib="true"/>
/// <reference lib="esnext" />
/// <reference lib="webworker" />

const sw = /** @type {ServiceWorkerGlobalScope} */ (/** @type {unknown} */ (self));

console.log('🔧 SvelteKit Service Worker loaded at:', new Date().toISOString());

// Install event
sw.addEventListener('install', (event) => {
  console.log('✅ Service Worker installing...', event);
  sw.skipWaiting();
});

// Activate event
sw.addEventListener('activate', (event) => {
  console.log('✅ Service Worker activating...', event);
  console.log('🔍 Service Worker state:', {
    scriptURL: sw.location.href,
    scope: sw.registration?.scope,
  });
  sw.clients.claim();
});

// Push event - handle incoming push notifications
sw.addEventListener('push', (event) => {
  console.log('📱 Push event received:', event);

  let notificationData = {
    title: 'Auto Apply',
    body: 'You have a new notification',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    tag: 'auto-apply-notification',
    data: {
      url: '/dashboard/notifications',
      timestamp: Date.now(),
    },
  };

  // Parse push data if available
  if (event.data) {
    console.log(event.data.text());
    try {
      // Try to get text first to see what we're dealing with
      const textData = event.data.text();
      console.log('📱 Raw push data:', textData);

      // Try to parse as JSON
      const data = JSON.parse(textData);
      console.log('📱 Parsed push data:', data);

      notificationData = {
        title: data.title || notificationData.title,
        body: data.message || data.body || notificationData.body,
        icon: data.icon || notificationData.icon,
        badge: data.badge || notificationData.badge,
        tag: data.tag || `notification-${data.id || Date.now()}`,
        data: {
          url: data.url || notificationData.data.url,
          notificationId: data.id,
          timestamp: Date.now(),
          ...data.data,
        },
      };
    } catch (error) {
      console.error('❌ Error parsing push data:', error);
      // If it's not JSON, treat the text as the message
      if (event.data) {
        try {
          const textData = event.data.text();
          if (textData) {
            notificationData.body = textData;
            notificationData.title = 'Auto Apply';
          }
        } catch (textError) {
          console.error('❌ Error getting text data:', textError);
        }
      }
    }
  }

  // Show the notification
  const showNotificationPromise = sw.registration
    .showNotification(notificationData.title, {
      body: notificationData.body,
      icon: notificationData.icon,
      badge: notificationData.badge,
      tag: notificationData.tag,
      data: notificationData.data,
      requireInteraction: false,
      silent: false,
    })
    .catch((error) => {
      console.error('❌ Error showing notification:', error);
    });

  event.waitUntil(showNotificationPromise);
});

// Notification click event
sw.addEventListener('notificationclick', (event) => {
  console.log('🔔 Notification clicked:', event);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Default action or 'view' action
  const urlToOpen = event.notification.data?.url || '/dashboard/notifications';

  event.waitUntil(
    sw.clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        console.log('🔍 Found clients:', clientList.length);

        // Check if there's already a window/tab open with the target URL or base domain
        for (const client of clientList) {
          const clientUrl = new URL(client.url);
          const targetUrl = new URL(urlToOpen, sw.location.origin);

          // If same origin, focus the existing window and navigate to the target URL
          if (clientUrl.origin === targetUrl.origin && 'focus' in client) {
            console.log('🎯 Focusing existing client and navigating to:', urlToOpen);
            client.focus();
            // Navigate to the specific URL if it's different
            if (client.url !== urlToOpen) {
              return client.navigate(urlToOpen);
            }
            return client;
          }
        }

        // If no existing window/tab, open a new one
        if (sw.clients.openWindow) {
          console.log('🆕 Opening new window for:', urlToOpen);
          return sw.clients.openWindow(urlToOpen);
        }
      })
      .catch((error) => {
        console.error('❌ Error handling notification click:', error);
      })
  );
});

// Background sync (optional - for offline functionality)
sw.addEventListener('sync', (event) => {
  console.log('🔄 Background sync event:', event.tag);

  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle background sync tasks here
      Promise.resolve()
    );
  }
});
