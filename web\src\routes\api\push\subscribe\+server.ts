import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const user = await getUserFromToken(cookies);
    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { subscription } = await request.json();

    // Save subscription to database
    await prisma.pushSubscription.upsert({
      where: { userId: user.id },
      update: {
        endpoint: subscription.endpoint,
        p256dh: subscription.keys.p256dh,
        auth: subscription.keys.auth,
      },
      create: {
        userId: user.id,
        endpoint: subscription.endpoint,
        p256dh: subscription.keys.p256dh,
        auth: subscription.keys.auth,
      },
    });

    // Update notification settings
    await prisma.notificationSettings.upsert({
      where: { userId: user.id },
      update: { pushEnabled: true },
      create: {
        userId: user.id,
        pushEnabled: true,
        emailEnabled: true,
        browserEnabled: true,
        jobMatchEnabled: true,
        applicationStatusEnabled: true,
        automationEnabled: true,
      },
    });

    console.log(`Push subscription saved for user ${user.id}`);
    return json({ success: true });
  } catch (error) {
    console.error('Error saving push subscription:', error);
    return json({ error: 'Failed to save subscription' }, { status: 500 });
  }
};
