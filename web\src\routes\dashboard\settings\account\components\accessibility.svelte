<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Form from '$lib/components/ui/form/index.js';
  import { Switch } from '$lib/components/ui/switch/index.js';
  import { Sun, Moon, Monitor } from 'lucide-svelte';
  import {
    store,
    setTheme,
    updateAccessibilitySettings,
    type ThemeMode,
    type AccessibilitySettings,
    type ViewMode,
  } from '$lib/stores/store';
  import {
    requestNotificationPermission,
    disableNotifications,
    showNotification,
  } from '$lib/push-notifications';
  import { toast } from 'svelte-sonner';
  import { Button } from '$lib/components/ui/button/index.js';
  import { onMount } from 'svelte';

  const { form, formData } = $props<{ form: any; formData: any }>();

  // Initialize the store from the form data when the component is first loaded
  if ($formData) {
    // Update the theme if provided
    if ($formData.theme) {
      setTheme($formData.theme as ThemeMode);
    }

    // Update accessibility settings
    const settings: Partial<AccessibilitySettings> = {};

    if ($formData.highContrast !== undefined)
      settings.highContrast = Boolean($formData.highContrast);
    if ($formData.reducedMotion !== undefined)
      settings.reducedMotion = Boolean($formData.reducedMotion);
    if ($formData.largeText !== undefined) settings.largeText = Boolean($formData.largeText);
    if ($formData.screenReader !== undefined)
      settings.screenReader = Boolean($formData.screenReader);

    // UI Preferences
    if ($formData.viewMode !== undefined) settings.viewMode = $formData.viewMode as ViewMode;

    if (Object.keys(settings).length > 0) {
      updateAccessibilitySettings(settings);
    }
  }

  // Handle theme change from the UI
  function handleThemeChange(value: ThemeMode) {
    console.log('handleThemeChange called with:', value);
    setTheme(value);

    // Update form data
    formData.update((f: any) => ({ ...f, theme: value }));

    // Submit the form
    const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
    if (submitButton) submitButton.click();
  }

  // Handle accessibility setting change
  function handleAccessibilityChange(setting: keyof AccessibilitySettings, value: any) {
    console.log(`Changing ${setting} to ${value}`);
    updateAccessibilitySettings({ [setting]: value });

    // Update form data
    formData.update((f: any) => ({ ...f, [setting]: value }));

    // Submit the form
    const submitButton = document.getElementById('submit-button') as HTMLButtonElement;
    if (submitButton) submitButton.click();
  }

  // Test notification function
  function testPushNotification() {
    const notification = showNotification('Auto Apply - Test Notification', {
      body: 'This is a test notification to verify your notification settings are working correctly.',
      tag: 'test-notification',
      requireInteraction: false,
    });

    if (notification) {
      // Handle notification click
      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      toast.success('Test notification sent! Check your browser notifications.');
    } else {
      toast.error('Notifications not available. Please enable notifications first.');
    }
  }
</script>

<Card.Root>
  <Card.Header class="p-6">
    <Card.Title>Accessibility Settings</Card.Title>
    <Card.Description>Customize your experience for better accessibility.</Card.Description>
  </Card.Header>
  <Card.Content class="space-y-6 p-6 pt-0">
    <Form.Field {form} name="theme">
      <div class="space-y-2">
        <div class="font-medium">Theme Preference</div>
        <Form.Description>Choose your preferred theme for the application</Form.Description>
        <div class="flex flex-col gap-4 pt-2 sm:flex-row">
          <!-- Light Theme Option -->
          <button
            type="button"
            class="hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 {$store &&
            $store.theme === 'light'
              ? 'border-primary'
              : 'border-muted'}"
            onclick={() => handleThemeChange('light')}>
            <Sun class="mb-2 h-6 w-6 text-yellow-500" />
            <span>Light</span>
          </button>

          <!-- Dark Theme Option -->
          <button
            type="button"
            class="hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 {$store &&
            $store.theme === 'dark'
              ? 'border-primary'
              : 'border-muted'}"
            onclick={() => handleThemeChange('dark')}>
            <Moon class="mb-2 h-6 w-6 text-blue-400" />
            <span>Dark</span>
          </button>

          <!-- System Theme Option -->
          <button
            type="button"
            class="hover:bg-accent flex cursor-pointer flex-col items-center justify-between rounded-md border-2 p-4 {$store &&
            $store.theme === 'system'
              ? 'border-primary'
              : 'border-muted'}"
            onclick={() => handleThemeChange('system')}>
            <Monitor class="mb-2 h-6 w-6 text-gray-500" />
            <span>System</span>
          </button>
        </div>
      </div>
      <Form.FieldErrors />
    </Form.Field>
    <Form.Field {form} name="highContrast">
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <div class="font-medium">High Contrast Mode</div>
          <Form.Description>Increase contrast for better visibility</Form.Description>
        </div>
        <Form.Control>
          <Switch
            checked={Boolean($formData.highContrast)}
            onCheckedChange={(checked) => handleAccessibilityChange('highContrast', checked)} />
        </Form.Control>
      </div>
      <Form.FieldErrors />
    </Form.Field>

    <Form.Field {form} name="pushNotifications">
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="space-y-0.5">
            <div class="font-medium">Push Notifications</div>
            <Form.Description>Enable or disable browser push notifications</Form.Description>
          </div>
          <Form.Control>
            <Switch
              checked={Boolean($formData.pushNotifications)}
              onCheckedChange={async (checked) => {
                if (checked) {
                  // Request permission first - don't turn on toggle until user accepts
                  const result = await requestNotificationPermission();
                  if (result.success) {
                    // Only update form AFTER permission is granted
                    formData.update((f: any) => ({ ...f, pushNotifications: true }));
                    const submitButton = document.getElementById(
                      'submit-button'
                    ) as HTMLButtonElement;
                    if (submitButton) submitButton.click();
                    toast.success('Notifications enabled successfully!');
                  } else {
                    // Keep the switch off if permission was denied or failed
                    formData.update((f: any) => ({ ...f, pushNotifications: false }));
                    toast.error(result.error || 'Failed to enable notifications');
                  }
                } else {
                  // Disable notifications
                  const result = await disableNotifications();
                  if (result.success) {
                    // Update form and save to DB
                    formData.update((f: any) => ({ ...f, pushNotifications: false }));
                    const submitButton = document.getElementById(
                      'submit-button'
                    ) as HTMLButtonElement;
                    if (submitButton) submitButton.click();
                    toast.success('Notifications disabled successfully');
                  } else {
                    // Reset the switch if disabling failed
                    formData.update((f: any) => ({ ...f, pushNotifications: true }));
                    toast.error(result.error || 'Failed to disable notifications');
                  }
                }
              }} />
          </Form.Control>
        </div>

        {#if Boolean($formData.pushNotifications)}
          <div class="flex justify-start">
            <Button type="button" variant="outline" size="sm" onclick={testPushNotification}>
              Test Push Notification
            </Button>
          </div>
        {/if}
      </div>
      <Form.FieldErrors />
    </Form.Field>
  </Card.Content>
</Card.Root>
