import { json } from '@sveltejs/kit';
import { getVapidPublicKey } from '$lib/server/push-notifications';
import type { RequestHandler } from './$types';

/**
 * Get VAPID public key for client-side push subscription
 */
export const GET: RequestHandler = async () => {
  try {
    const publicKey = getVapidPublicKey();
    
    if (!publicKey) {
      return json({ error: 'VAPID public key not configured' }, { status: 500 });
    }

    return json({ publicKey });
  } catch (error) {
    console.error('Error getting VAPID public key:', error);
    return json({ error: 'Failed to get VAPID public key' }, { status: 500 });
  }
};
