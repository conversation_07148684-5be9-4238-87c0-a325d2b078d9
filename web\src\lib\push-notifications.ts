import { browser } from '$app/environment';

export function isNotificationSupported(): boolean {
  return browser && 'Notification' in window;
}

export function getNotificationPermission(): NotificationPermission {
  if (!browser || !('Notification' in window)) return 'default';
  return Notification.permission;
}

export async function subscribeToPush(): Promise<{
  success: boolean;
  error?: string;
}> {
  if (!isNotificationSupported()) {
    return { success: false, error: 'Notifications not supported' };
  }

  try {
    const permission = await Notification.requestPermission();
    if (permission !== 'granted') {
      return { success: false, error: 'Notification permission denied' };
    }

    // Create a subscription object to send to server
    const subscription = {
      endpoint: 'web-push-endpoint',
      keys: {
        p256dh: 'user-public-key',
        auth: 'user-auth-secret',
      },
    };

    const response = await fetch('/api/push/subscribe', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ subscription }),
    });

    if (!response.ok) throw new Error('Failed to save subscription');

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function unsubscribeFromPush(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const response = await fetch('/api/push/unsubscribe', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });

    if (!response.ok) throw new Error('Failed to unsubscribe');

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to unsubscribe',
    };
  }
}

export function showNotification(
  title: string,
  options?: NotificationOptions
): Notification | null {
  if (!isNotificationSupported() || Notification.permission !== 'granted') {
    return null;
  }

  return new Notification(title, {
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    ...options,
  });
}
