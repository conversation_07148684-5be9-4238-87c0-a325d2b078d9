import { browser } from '$app/environment';

export function isNotificationSupported(): boolean {
  return browser && 'Notification' in window;
}

export function getNotificationPermission(): NotificationPermission {
  if (!isNotificationSupported()) return 'default';
  return Notification.permission;
}

export async function requestNotificationPermission(): Promise<{
  success: boolean;
  error?: string;
  permission?: NotificationPermission;
}> {
  if (!isNotificationSupported()) {
    return { success: false, error: 'Notifications not supported' };
  }

  try {
    const permission = await Notification.requestPermission();

    if (permission === 'granted') {
      // Save to user preferences
      await fetch('/api/notifications/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pushEnabled: true }),
      });

      return { success: true, permission };
    } else {
      return { success: false, error: 'Permission denied', permission };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function disableNotifications(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Update user preferences
    await fetch('/api/notifications/settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ pushEnabled: false }),
    });

    return { success: true };
  } catch (error) {
    console.error('Error disabling notifications:', error);
    return { success: false, error: 'Failed to disable notifications' };
  }
}

export function showNotification(
  title: string,
  options?: NotificationOptions
): Notification | null {
  if (!isNotificationSupported() || Notification.permission !== 'granted') {
    return null;
  }

  return new Notification(title, {
    icon: '/assets/favicon/favicon.ico',
    badge: '/assets/favicon/favicon.ico',
    ...options,
  });
}

export async function checkServiceWorkerRegistration() {
  if (!('serviceWorker' in navigator)) {
    console.error('Service Worker is not supported');
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.getSubscription();
    console.log('Subscription:', subscription);

    return subscription !== null;
  } catch (error) {
    console.error('Failed to get Service Worker registration:', error);
    return false;
  }
}

export async function subscribeUser() {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: process.env.VAPID_PUBLIC_KEY,
      });
      console.log('User subscribed:', subscription);
    } catch (error) {
      console.error('Failed to subscribe user:', error);
    }
  } else {
    console.error('Service Worker is not supported');
  }
}
