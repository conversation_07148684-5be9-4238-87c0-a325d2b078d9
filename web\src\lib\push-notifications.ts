import { browser } from '$app/environment';

export function isNotificationSupported(): boolean {
  return browser && 'Notification' in window;
}

export function getNotificationPermission(): NotificationPermission {
  if (!isNotificationSupported()) return 'default';
  return Notification.permission;
}

export async function requestNotificationPermission(): Promise<{
  success: boolean;
  error?: string;
  permission?: NotificationPermission;
}> {
  if (!isNotificationSupported()) {
    return { success: false, error: 'Notifications not supported' };
  }

  try {
    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      // Save to user preferences
      await fetch('/api/notifications/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pushEnabled: true }),
      });
      
      return { success: true, permission };
    } else {
      return { success: false, error: 'Permission denied', permission };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function disableNotifications(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Update user preferences
    await fetch('/api/notifications/settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ pushEnabled: false }),
    });

    return { success: true };
  } catch (error) {
    console.error('Error disabling notifications:', error);
    return { success: false, error: 'Failed to disable notifications' };
  }
}

export function showNotification(title: string, options?: NotificationOptions): Notification | null {
  if (!isNotificationSupported() || Notification.permission !== 'granted') {
    return null;
  }

  return new Notification(title, {
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    ...options,
  });
}
