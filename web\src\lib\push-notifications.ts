import { browser } from '$app/environment';

const VAPID_PUBLIC_KEY =
  'BIaqHOyyyneXuUSk36CGTJTuoumbWmnQZaOJjEZZ-UOhC2RnXTCURthZsm9iPstH4oWJ9QRgP-QIvsCbV3YV3h4';

function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(new ArrayBuffer(rawData.length));
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

export function isPushNotificationSupported(): boolean {
  if (!browser) return false;
  if (location.protocol !== 'https:' && location.hostname !== 'localhost') return false;
  return 'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window;
}

export function getNotificationPermission(): NotificationPermission {
  if (!browser || !('Notification' in window)) return 'default';
  return Notification.permission;
}

export async function requestPushNotificationPermission(): Promise<{
  success: boolean;
  error?: string;
  permission?: NotificationPermission;
}> {
  if (!isPushNotificationSupported()) {
    return { success: false, error: 'Push notifications not supported' };
  }

  try {
    let permission = getNotificationPermission();
    if (permission === 'denied') {
      return { success: false, error: 'Notifications blocked', permission };
    }
    if (permission === 'default') {
      permission = await Notification.requestPermission();
    }
    if (permission !== 'granted') {
      return { success: false, error: 'Permission not granted', permission };
    }

    // Ensure service worker is active before subscribing
    let registration = await navigator.serviceWorker.ready;

    // If no active service worker, wait for it to activate
    if (!registration.active) {
      console.log('⏳ Waiting for service worker to activate...');
      await new Promise((resolve) => {
        const checkActive = () => {
          if (registration.active) {
            resolve(true);
          } else {
            setTimeout(checkActive, 100);
          }
        };
        checkActive();
      });
    }

    console.log('✅ Service worker is active, creating subscription...');
    const vapidKey = urlBase64ToUint8Array(VAPID_PUBLIC_KEY);
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: vapidKey,
    });

    console.log('✅ Push subscription created:', subscription.endpoint);

    // Just save notification settings to user preferences - no subscription storage needed
    const response = await fetch('/api/notifications/settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ pushEnabled: true }),
    });

    if (!response.ok) throw new Error('Failed to save settings');

    return { success: true, permission };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function unregisterPushNotifications(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const registration = await navigator.serviceWorker.ready;
    const subscription = await registration.pushManager.getSubscription();
    if (subscription) {
      await subscription.unsubscribe();
    }

    // Update user preferences
    await fetch('/api/notifications/settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ pushEnabled: false }),
    });

    return { success: true };
  } catch (error) {
    return { success: false, error: 'Unregister failed' };
  }
}

export async function getPushNotificationStatus(): Promise<{
  supported: boolean;
  permission: NotificationPermission;
  hasSubscription: boolean;
  serviceWorkerRegistered: boolean;
}> {
  const supported = isPushNotificationSupported();
  const permission = getNotificationPermission();
  let hasSubscription = false;
  let serviceWorkerRegistered = false;

  if (supported) {
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      serviceWorkerRegistered = !!registration;
      if (registration) {
        const subscription = await registration.pushManager.getSubscription();
        hasSubscription = !!subscription;
      }
    } catch (error) {
      console.error('Error getting push notification status:', error);
    }
  }

  return { supported, permission, hasSubscription, serviceWorkerRegistered };
}
