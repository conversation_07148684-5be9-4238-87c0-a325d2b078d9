/// <reference types="@sveltejs/kit" />
/// <reference no-default-lib="true"/>
/// <reference lib="esnext" />
/// <reference lib="webworker" />

const sw = self as unknown as ServiceWorkerGlobalScope;

// Install event
sw.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  sw.skipWaiting();
});

// Activate event
sw.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  sw.clients.claim();
});

// Push event - handle incoming push notifications
sw.addEventListener('push', (event) => {
  console.log('Push event received:', event);

  let notificationData = {
    title: 'Auto Apply',
    body: 'You have a new notification',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    tag: 'auto-apply-notification',
    data: {
      url: '/dashboard/notifications',
      timestamp: Date.now(),
    },
  };

  // Parse push data if available
  if (event.data) {
    try {
      const data = JSON.parse(event.data.text());
      notificationData = {
        title: data.title || notificationData.title,
        body: data.message || data.body || notificationData.body,
        icon: data.icon || notificationData.icon,
        badge: data.badge || notificationData.badge,
        tag: data.tag || `notification-${data.id || Date.now()}`,
        data: {
          url: data.url || notificationData.data.url,
          notificationId: data.id,
          timestamp: Date.now(),
          ...data.data,
        },
      };
    } catch (error) {
      console.error('Error parsing push data:', error);
    }
  }

  // Show the notification
  const showNotificationPromise = sw.registration.showNotification(notificationData.title, {
    body: notificationData.body,
    icon: notificationData.icon,
    badge: notificationData.badge,
    tag: notificationData.tag,
    data: notificationData.data,
    requireInteraction: false,
    silent: false,
  });

  event.waitUntil(showNotificationPromise);
});

// Notification click event
sw.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Default action - open the URL
  const urlToOpen = event.notification.data?.url || '/dashboard/notifications';

  event.waitUntil(
    sw.clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open
      for (const client of clientList) {
        const clientUrl = new URL(client.url);
        const targetUrl = new URL(urlToOpen, sw.location.origin);

        if (clientUrl.origin === targetUrl.origin && 'focus' in client) {
          client.focus();
          if (client.url !== urlToOpen) {
            return client.navigate(urlToOpen);
          }
          return client;
        }
      }

      // If no existing window/tab, open a new one
      if (sw.clients.openWindow) {
        return sw.clients.openWindow(urlToOpen);
      }
    })
  );
});
