import { json } from '@sveltejs/kit';
import { getUserFromToken } from '$lib/server/auth';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

/**
 * Update notification settings (including push notification preference)
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const user = await getUserFromToken(cookies);
    if (!user) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { pushEnabled } = await request.json();

    await prisma.notificationSettings.upsert({
      where: { userId: user.id },
      update: { pushEnabled },
      create: {
        userId: user.id,
        pushEnabled,
        emailEnabled: true,
        browserEnabled: true,
        jobMatchEnabled: true,
        applicationStatusEnabled: true,
        automationEnabled: true,
      },
    });

    console.log(`Push notifications ${pushEnabled ? 'enabled' : 'disabled'} for user ${user.id}`);
    return json({ success: true });
  } catch (error) {
    console.error('Error updating notification settings:', error);
    return json({ error: 'Failed to update settings' }, { status: 500 });
  }
};
